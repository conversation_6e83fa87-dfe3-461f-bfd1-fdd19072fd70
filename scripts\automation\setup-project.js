#!/usr/bin/env node

/**
 * Comprehensive Project Setup Automation
 * Automates the entire project setup process
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

class ProjectSetup {
  constructor() {
    this.config = {
      deepseekApiKey: '',
      openaiApiKey: '',
      nextauthSecret: '',
      githubClientId: '',
      githubClientSecret: '',
      googleClientId: '',
      googleClientSecret: '',
      mongodbUri: '',
      vercelToken: '',
      slackWebhook: ''
    };
  }

  async run() {
    console.log('🚀 AI CodeLearner - Automated Project Setup\n');
    
    try {
      await this.checkPrerequisites();
      await this.collectConfiguration();
      await this.setupEnvironment();
      await this.installDependencies();
      await this.runTests();
      await this.setupGitHooks();
      await this.generateDocumentation();
      
      console.log('\n✅ Project setup completed successfully!');
      console.log('\n📋 Next steps:');
      console.log('1. Run: npm run dev');
      console.log('2. Visit: http://localhost:3000');
      console.log('3. Test AI features');
      console.log('4. Deploy to Vercel');
      
    } catch (error) {
      console.error('\n❌ Setup failed:', error.message);
      process.exit(1);
    } finally {
      rl.close();
    }
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      throw new Error(`Node.js 18+ required. Current version: ${nodeVersion}`);
    }
    
    // Check npm
    try {
      execSync('npm --version', { stdio: 'ignore' });
    } catch {
      throw new Error('npm is not installed');
    }
    
    // Check git
    try {
      execSync('git --version', { stdio: 'ignore' });
    } catch {
      throw new Error('git is not installed');
    }
    
    console.log('✅ Prerequisites check passed');
  }

  async collectConfiguration() {
    console.log('\n🔧 Configuration Setup');
    console.log('Please provide the following configuration values:\n');
    
    // DeepSeek API Key
    this.config.deepseekApiKey = await this.prompt(
      '🤖 DeepSeek API Key (get from https://platform.deepseek.com/): '
    );
    
    // Optional OpenAI API Key
    const useOpenAI = await this.prompt('🔄 Also configure OpenAI? (y/n): ');
    if (useOpenAI.toLowerCase() === 'y') {
      this.config.openaiApiKey = await this.prompt(
        '🧠 OpenAI API Key (get from https://platform.openai.com/): '
      );
    }
    
    // NextAuth Secret
    this.config.nextauthSecret = await this.prompt(
      '🔐 NextAuth Secret (random string): '
    ) || this.generateRandomString(32);
    
    // OAuth Providers (optional)
    const setupOAuth = await this.prompt('🔑 Setup OAuth providers? (y/n): ');
    if (setupOAuth.toLowerCase() === 'y') {
      this.config.githubClientId = await this.prompt('GitHub Client ID: ');
      this.config.githubClientSecret = await this.prompt('GitHub Client Secret: ');
      this.config.googleClientId = await this.prompt('Google Client ID: ');
      this.config.googleClientSecret = await this.prompt('Google Client Secret: ');
    }
    
    // Database (optional)
    const setupDB = await this.prompt('🗄️  Setup MongoDB? (y/n): ');
    if (setupDB.toLowerCase() === 'y') {
      this.config.mongodbUri = await this.prompt('MongoDB URI: ');
    }
    
    // Deployment (optional)
    const setupDeploy = await this.prompt('🚀 Setup deployment automation? (y/n): ');
    if (setupDeploy.toLowerCase() === 'y') {
      this.config.vercelToken = await this.prompt('Vercel Token: ');
      this.config.slackWebhook = await this.prompt('Slack Webhook URL (optional): ');
    }
  }

  async setupEnvironment() {
    console.log('\n📝 Creating environment files...');
    
    const envContent = this.generateEnvContent();
    const envPath = path.join(process.cwd(), '.env.local');
    
    // Backup existing .env.local
    if (fs.existsSync(envPath)) {
      const backupPath = `${envPath}.backup.${Date.now()}`;
      fs.copyFileSync(envPath, backupPath);
      console.log(`📋 Backed up existing .env.local to ${path.basename(backupPath)}`);
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log('✅ Environment file created');
  }

  generateEnvContent() {
    let content = '# AI CodeLearner - Environment Configuration\n';
    content += '# Generated by automated setup\n\n';
    
    // AI Configuration
    content += '# AI Configuration\n';
    if (this.config.deepseekApiKey) {
      content += `DEEPSEEK_API_KEY=${this.config.deepseekApiKey}\n`;
    }
    if (this.config.openaiApiKey) {
      content += `OPENAI_API_KEY=${this.config.openaiApiKey}\n`;
    }
    content += '\n';
    
    // NextAuth Configuration
    content += '# NextAuth Configuration\n';
    content += `NEXTAUTH_URL=http://localhost:3000\n`;
    content += `NEXTAUTH_SECRET=${this.config.nextauthSecret}\n\n`;
    
    // OAuth Providers
    if (this.config.githubClientId) {
      content += '# OAuth Providers\n';
      content += `GITHUB_CLIENT_ID=${this.config.githubClientId}\n`;
      content += `GITHUB_CLIENT_SECRET=${this.config.githubClientSecret}\n`;
    }
    if (this.config.googleClientId) {
      content += `GOOGLE_CLIENT_ID=${this.config.googleClientId}\n`;
      content += `GOOGLE_CLIENT_SECRET=${this.config.googleClientSecret}\n`;
    }
    if (this.config.githubClientId || this.config.googleClientId) {
      content += '\n';
    }
    
    // Database
    if (this.config.mongodbUri) {
      content += '# Database Configuration\n';
      content += `MONGODB_URI=${this.config.mongodbUri}\n\n`;
    }
    
    return content;
  }

  async installDependencies() {
    console.log('\n📦 Installing dependencies...');
    
    try {
      execSync('npm install', { stdio: 'inherit' });
      console.log('✅ Dependencies installed');
    } catch (error) {
      throw new Error('Failed to install dependencies');
    }
  }

  async runTests() {
    console.log('\n🧪 Running configuration tests...');
    
    try {
      // Test DeepSeek configuration
      if (this.config.deepseekApiKey) {
        execSync('npm run test-deepseek', { stdio: 'inherit' });
      }
      
      // Test OpenAI configuration
      if (this.config.openaiApiKey) {
        execSync('npm run test-openai', { stdio: 'inherit' });
      }
      
      console.log('✅ Configuration tests passed');
    } catch (error) {
      console.warn('⚠️  Some tests failed, but setup will continue');
    }
  }

  async setupGitHooks() {
    console.log('\n🪝 Setting up Git hooks...');
    
    const hooksDir = path.join(process.cwd(), '.git', 'hooks');
    
    if (!fs.existsSync(hooksDir)) {
      console.log('⚠️  Git repository not found, skipping hooks setup');
      return;
    }
    
    // Pre-commit hook
    const preCommitHook = `#!/bin/sh
# Auto-generated pre-commit hook
echo "🔍 Running pre-commit checks..."

# Run linting
npm run lint
if [ $? -ne 0 ]; then
  echo "❌ Linting failed"
  exit 1
fi

# Run type checking
npm run type-check
if [ $? -ne 0 ]; then
  echo "❌ Type checking failed"
  exit 1
fi

echo "✅ Pre-commit checks passed"
`;
    
    const preCommitPath = path.join(hooksDir, 'pre-commit');
    fs.writeFileSync(preCommitPath, preCommitHook);
    fs.chmodSync(preCommitPath, '755');
    
    console.log('✅ Git hooks configured');
  }

  async generateDocumentation() {
    console.log('\n📚 Generating project documentation...');
    
    const setupSummary = `# Project Setup Summary

## Configuration
- **AI Provider**: ${this.config.deepseekApiKey ? 'DeepSeek' : 'Not configured'}
- **OpenAI Backup**: ${this.config.openaiApiKey ? 'Configured' : 'Not configured'}
- **Authentication**: ${this.config.nextauthSecret ? 'Configured' : 'Not configured'}
- **OAuth Providers**: ${this.config.githubClientId || this.config.googleClientId ? 'Configured' : 'Not configured'}
- **Database**: ${this.config.mongodbUri ? 'MongoDB configured' : 'Not configured'}

## Quick Commands
\`\`\`bash
# Start development server
npm run dev

# Run tests
npm run test

# Build for production
npm run build

# Deploy to Vercel
vercel --prod
\`\`\`

## Generated Files
- \`.env.local\` - Environment configuration
- \`.git/hooks/pre-commit\` - Git pre-commit hook

## Next Steps
1. Test the application: \`npm run dev\`
2. Configure additional features as needed
3. Set up CI/CD pipeline
4. Deploy to production

Generated on: ${new Date().toISOString()}
`;
    
    fs.writeFileSync('SETUP_SUMMARY.md', setupSummary);
    console.log('✅ Documentation generated');
  }

  generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  prompt(question) {
    return new Promise((resolve) => {
      rl.question(question, (answer) => {
        resolve(answer.trim());
      });
    });
  }
}

// Run the setup if called directly
if (require.main === module) {
  const setup = new ProjectSetup();
  setup.run().catch(console.error);
}

module.exports = ProjectSetup;
