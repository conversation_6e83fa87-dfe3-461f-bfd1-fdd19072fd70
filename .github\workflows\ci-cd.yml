name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  # Code Quality & Testing
  quality-check:
    runs-on: ubuntu-latest
    name: Code Quality & Testing
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run ESLint
      run: npm run lint
      
    - name: Run TypeScript check
      run: npm run type-check
      
    - name: Run tests
      run: npm run test
      env:
        DEEPSEEK_API_KEY: ${{ secrets.DEEPSEEK_API_KEY }}
        NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
        
    - name: Test API endpoints
      run: |
        npm run test:api
        
    - name: Build application
      run: npm run build
      env:
        DEEPSEEK_API_KEY: ${{ secrets.DEEPSEEK_API_KEY }}
        NEXTAUTH_URL: https://your-app.vercel.app
        NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    name: Security Scan
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run npm audit
      run: npm audit --audit-level=moderate
      
    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  # Performance Testing
  performance-test:
    runs-on: ubuntu-latest
    name: Performance Testing
    needs: [quality-check]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      env:
        DEEPSEEK_API_KEY: ${{ secrets.DEEPSEEK_API_KEY }}
        
    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Deploy to Preview (for PRs)
  deploy-preview:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    name: Deploy Preview
    needs: [quality-check, security-scan]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install Vercel CLI
      run: npm install --global vercel@latest
      
    - name: Pull Vercel Environment Information
      run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
      
    - name: Build Project Artifacts
      run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
      
    - name: Deploy Project Artifacts to Vercel
      id: deploy
      run: |
        url=$(vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }})
        echo "preview_url=$url" >> $GITHUB_OUTPUT
        
    - name: Comment PR with preview URL
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '🚀 Preview deployment ready! \n\n🔗 **Preview URL:** ${{ steps.deploy.outputs.preview_url }}'
          })

  # Deploy to Production
  deploy-production:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    name: Deploy to Production
    needs: [quality-check, security-scan, performance-test]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install Vercel CLI
      run: npm install --global vercel@latest
      
    - name: Pull Vercel Environment Information
      run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      
    - name: Build Project Artifacts
      run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      
    - name: Deploy Project Artifacts to Vercel
      run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
      
    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Release v${{ github.run_number }}
        body: |
          🚀 Automated release from main branch
          
          **Changes in this release:**
          ${{ github.event.head_commit.message }}
          
          **Deployed to:** https://your-app.vercel.app
        draft: false
        prerelease: false

  # Notify on deployment
  notify:
    runs-on: ubuntu-latest
    name: Notify Team
    needs: [deploy-production]
    if: always()
    
    steps:
    - name: Notify Slack on success
      if: needs.deploy-production.result == 'success'
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: '✅ Production deployment successful!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        
    - name: Notify Slack on failure
      if: needs.deploy-production.result == 'failure'
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: '❌ Production deployment failed!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
