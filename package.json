{"name": "saas-learning-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "setup": "node setup-env.js", "setup:auto": "node scripts/automation/setup-project.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:api": "node scripts/test/api-tests.js", "test-openai": "node test-openai.js", "test-deepseek": "node test-deepseek.js", "test-env": "node test-env.js", "deploy": "node scripts/automation/deploy.js", "deploy:vercel": "node scripts/automation/deploy.js vercel production", "deploy:preview": "node scripts/automation/deploy.js vercel preview", "deploy:netlify": "node scripts/automation/deploy.js netlify production", "deploy:docker": "node scripts/automation/deploy.js docker production", "analyze": "npm run build && npx @next/bundle-analyzer", "analyze:build": "ANALYZE=true npm run build", "security:audit": "npm audit --audit-level=moderate", "security:fix": "npm audit fix", "db:migrate": "node scripts/database/migrate.js", "db:seed": "node scripts/database/seed.js", "backup:env": "node scripts/backup/backup-env.js", "restore:env": "node scripts/backup/restore-env.js", "health-check": "node scripts/monitoring/health-check.js", "performance:test": "node scripts/performance/lighthouse.js", "clean": "rm -rf .next node_modules/.cache", "fresh-install": "rm -rf node_modules package-lock.json && npm install", "update-deps": "npx npm-check-updates -u && npm install", "optimize": "npm run lint:fix && npm run type-check && npm run build", "precommit": "npm run lint && npm run type-check", "prepare": "husky install || true"}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@codemirror/lang-cpp": "^6.0.3", "@codemirror/lang-java": "^6.0.2", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-php": "^6.0.2", "@codemirror/lang-python": "^6.2.1", "@codemirror/lang-rust": "^6.0.2", "@monaco-editor/react": "^4.7.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@uiw/react-codemirror": "^4.24.2", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "critters": "^0.0.23", "dotenv": "^17.2.0", "framer-motion": "^10.16.4", "lottie-react": "^2.4.1", "mongodb": "^6.17.0", "next": "^15.4.1", "next-auth": "^4.24.11", "openai": "^4.104.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "tailwindcss": "^3.4.0", "typescript": "^5.0.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.1", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "eslint": "^8.0.0", "eslint-config-next": "^15.4.1", "husky": "^8.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "lighthouse": "^11.0.0", "lint-staged": "^15.0.0", "node-fetch": "^3.3.0"}}